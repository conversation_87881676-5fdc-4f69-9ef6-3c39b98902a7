// Bryzos Connectivity Troubleshooter - Endpoint Configuration
// Single external configuration file with Base64 encoding for security

const fs = require('fs');
const path = require('path');

/**
 * Base64 utility functions for encoding/decoding endpoint configurations
 */
const ConfigUtils = {
  encode: (data) => {
    return Buffer.from(JSON.stringify(data)).toString('base64');
  },

  decode: (encodedData) => {
    try {
      return JSON.parse(Buffer.from(encodedData, 'base64').toString('utf8'));
    } catch (error) {
      console.error('Failed to decode configuration:', error);
      return null;
    }
  },
};

/**
 * Load configuration from external bz.config file
 * This allows for easy deployment without rebuilding the application
 */
function loadExternalConfig() {
  try {
    // Look for bz.config in the application root directory
    const configPath = path.join(process.cwd(), 'bz.config');
    
    if (!fs.existsSync(configPath)) {
      console.error('Configuration file not found:', configPath);
      return null;
    }

    // Read the Base64 encoded configuration
    const encodedConfig = fs.readFileSync(configPath, 'utf8').trim();
    
    if (!encodedConfig) {
      console.error('Configuration file is empty:', configPath);
      return null;
    }

    // Decode the configuration
    const config = ConfigUtils.decode(encodedConfig);
    
    if (!config) {
      console.error('Failed to decode configuration from:', configPath);
      return null;
    }

    console.log(`✅ Loaded configuration with ${config.length} endpoints from: ${configPath}`);
    return config;
    
  } catch (error) {
    console.error('Error loading external configuration:', error.message);
    return null;
  }
}

/**
 * Get endpoint configuration
 * Loads from external bz.config file for maximum deployment flexibility
 */
function getEndpointConfiguration() {
  const config = loadExternalConfig();
  
  if (!config) {
    console.warn('⚠️  Using fallback configuration - external config failed to load');
    // Fallback to a minimal configuration if external config fails
    return [
      {
        id: 'fallback-test',
        displayName: 'Fallback Test',
        subdomain: 'httpbin.org',
        url: 'https://httpbin.org/get',
        method: 'GET',
        testMethod: 'testImageKitVideo'
      }
    ];
  }
  
  return config;
}

/**
 * Export configuration utilities and main function
 */
module.exports = {
  ConfigUtils,
  loadExternalConfig,
  getEndpointConfiguration,
  
  // Legacy compatibility - returns the loaded configuration
  getEndpoints: getEndpointConfiguration
};
