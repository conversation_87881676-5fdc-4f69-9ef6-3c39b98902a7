const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Network testing functions
  testConnectivity: (urls) => ipcRenderer.invoke('test-connectivity', urls),
  testDNS: (hostnames) => ipcRenderer.invoke('test-dns', hostnames),
  testCertificates: (urls) => ipc<PERSON>enderer.invoke('test-certificates', urls),
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  
  // Configuration functions
  saveConfig: (config) => ipcRenderer.invoke('save-config', config),
  loadConfig: () => ipcRenderer.invoke('load-config'),
  
  // Utility functions
  exportReport: (data) => ipcRenderer.invoke('export-report', data),
  
  // Event listeners
  onTestProgress: (callback) => ipcRenderer.on('test-progress', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),

  // Theme functions
  onThemeChange: (callback) => ipcRenderer.on('theme-changed', (event, theme) => callback(theme))
});

// Expose version info
contextBridge.exposeInMainWorld('appInfo', {
  version: process.env.npm_package_version || '1.0.0',
  platform: process.platform,
  arch: process.arch
});
