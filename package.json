{"name": "bryzos-conn-troubleshoot", "version": "1.0.0", "description": "Bryzos Connectivity Troubleshooter - Desktop application for diagnosing network connectivity issues", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "config:create": "node scripts/config-converter.js config.json"}, "keywords": ["connectivity", "troubleshoot", "network", "diagnostics", "electron"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"dns": "^0.2.2", "socket.io-client": "^4.8.1"}, "build": {"appId": "com.bryzos.conn-troubleshoot", "productName": "Bryzos Connectivity Troubleshooter", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}