<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bryzos Connectivity Troubleshooter</title>
   <link href="https://fonts.googleapis.com/css?family=Noto+Sans:300,400,500,600,700,900&display=swap"
    rel="stylesheet">
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- Sidebar Navigation -->
      <nav class="sidebar">
        <div class="app-header">
          <h1 class="app-title"><img src="../../assets/logo.png" alt="Bryzos Logo"/></h1>
          <p class="app-subtitle">Connectivity Troubleshooter</p>
        </div>

        <ul class="nav-menu">
          <li class="nav-item">
            <a
              href="#dashboard"
              class="nav-link active"
              data-section="dashboard"
            >
              <span class="nav-icon">🏠</span>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a
              href="#connectivity"
              class="nav-link"
              data-section="connectivity"
            >
              <span class="nav-icon">🌐</span>
              Connectivity Tests
            </a>
          </li>
        </ul>

        <div class="sidebar-footer">
          <button class="btn btn-primary" id="run-all-tests">
            Run All Tests
          </button>
        </div>
      </nav>

      <!-- Main Content Area -->
      <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
          <div class="app-header-main">
            <div>
              <h2 class="app-title">System Overview</h2>
              <p class="app-subtitle">
                Overall connectivity status and quick diagnostics
              </p>
            </div>
            <div>
              <span id="last-updated">Last updated: Never</span>
            </div>
          </div>

          <!-- Status Grid -->
          <div class="status-grid">
            <div class="status-card" id="connectivity-status">
              <div class="status-header">
                <h3 class="status-title">Connectivity</h3>
                <div class="status-indicator" id="connectivity-indicator"></div>
              </div>
              <div class="status-content" id="connectivity-content">
                Click "Run All Tests" to check connectivity status
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="test-results">
            <div class="test-results-header">Quick Actions</div>
            <div class="test-results-body">
              <div class="btn-group">
                <button class="btn btn-primary" id="quick-connectivity">
                  Test Connectivity
                </button>
                <button class="btn btn-secondary" id="export-report">
                  Export Report
                </button>
              </div>
            </div>
          </div>
        </section>

        <!-- Connectivity Section -->
        <section id="connectivity-section" class="content-section">
          <div class="app-header">
            <div>
              <h2 class="app-title">Connectivity Tests</h2>
              <p class="app-subtitle">
                Test HTTP/HTTPS connectivity to configured URLs
              </p>
            </div>
          </div>

          <div class="test-results">
            <div class="test-results-header">
              <span>Connectivity Test Results</span>
              <button class="btn btn-primary" id="run-connectivity-tests">
                Run Tests
              </button>
            </div>
            <div class="test-results-body" id="connectivity-results">
              <p class="text-center">
                Click "Run Tests" to check connectivity.
              </p>
            </div>
          </div>
        </section>

        <!-- DNS Section -->
        <section id="dns-section" class="content-section">
          <div class="app-header">
            <div>
              <h2 class="app-title">DNS Resolution</h2>
              <p class="app-subtitle">
                Test domain name resolution for configured URLs
              </p>
            </div>
          </div>

          <div class="test-results">
            <div class="test-results-header">
              <span>DNS Test Results</span>
              <button class="btn btn-primary" id="run-dns-tests">
                Run Tests
              </button>
            </div>
            <div class="test-results-body" id="dns-results">
              <p class="text-center">
                Click "Run Tests" to check DNS resolution.
              </p>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- Progress Modal -->
    <div id="progress-modal" class="modal" style="display: none">
      <div class="modal-content">
        <h3>Running Tests...</h3>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <p id="progress-text">Initializing...</p>
      </div>
    </div>

    <script src="app.js"></script>
  </body>
</html>
